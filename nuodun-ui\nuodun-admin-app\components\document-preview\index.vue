<template>
  <view class="document-preview">
    <view class="file-card">
      <view class="file-card-info">
        <text class="file-card-icon">{{ fileIcon }}</text>
        <text class="file-card-name">{{ fileName }}</text>
        <text v-if="!isSupportedFileType" class="file-type-tip">暂不支持预览</text>
      </view>
      <view class="file-card-btn" @tap="viewFile" v-if="allowView && isSupportedFileType">
        查看
      </view>
      <view class="file-card-btn download-btn" @tap="downloadFile" v-if="allowDownload">
        下载
      </view>
      <view class="file-card-btn delete-btn" @tap="deleteFile" v-if="allowDelete">
        删除
      </view>
    </view>
  </view>
</template>

<script setup>
import {computed, getCurrentInstance} from 'vue'
import {createModal} from '@/common/modal'
import {getFileType} from '@/common/fileUtils.js'
import {getFileTypeIcon} from '@/common/fileUtils'

// 定义事件
const emit = defineEmits(['delete', 'save'])
import config from '@/config/environment'
import {downloadAndOpenFile} from '@/common/fileUtils'

const {proxy} = getCurrentInstance()
const toast = proxy.toast

const props = defineProps({
  // 文件路径
  filePath: {
    type: String,
    default: ''
  },
  // 文件名称
  fileName: {
    type: String,
    default: '文件'
  },
  // 文件URL前缀
  urlPrefix: {
    type: String,
    default: ''
  },
  // 是否允许查看
  allowView: {
    type: Boolean,
    default: true
  },
  // 是否允许下载
  allowDownload: {
    type: Boolean,
    default: false
  },
  // 是否允许删除
  allowDelete: {
    type: Boolean,
    default: false
  }
})

// 获取文件类型
const fileType = computed(() => {
  return getFileType(props.filePath)
})

// 判断是否为支持的文件类型
const isSupportedFileType = computed(() => {
  return ['image', 'document'].includes(fileType.value)
})

// 获取文件图标
const fileIcon = computed(() => {
  return getFileTypeIcon(props.fileName)
})

// 获取文件完整URL
const fullFileUrl = computed(() => {
  if (!props.filePath) return ''

  // 如果filePath已经包含前缀，则直接返回
  if (props.filePath.startsWith('http://') || props.filePath.startsWith('https://')) {
    return props.filePath
  }

  // 使用传入的前缀或环境配置
  return (props.urlPrefix || config.fileUrl || '') + props.filePath
})

// 查看文件
const viewFile = () => {
  if (!props.filePath) {
    toast.show('文件路径为空')
    return
  }

  // 检查文件类型是否支持预览
  if (!isSupportedFileType.value) {
    const fileTypeName = getFileTypeName(fileType.value)
    toast.show(`暂不支持打开 ${fileTypeName} 文件，请下载后查看`)
    return
  }

  let filePath = props.filePath

  // 如果不是完整的URL，拼接文件服务器地址
  if (!filePath.startsWith('http')) {
    filePath = fullFileUrl.value
  }

  // 如果是图片文件，使用图片预览
  if (fileType.value === 'image') {
    uni.previewImage({
      urls: [filePath],
      current: filePath,
      fail: (err) => {
        console.error('图片预览失败:', err)
        toast.show('图片预览失败')
      }
    })
  } else if (fileType.value === 'document') {
    // 文档文件直接预览
    previewDocument(filePath)
  } else {
    const fileTypeName = getFileTypeName(fileType.value)
    toast.show(`暂不支持打开 ${fileTypeName} 文件，请下载后查看`)
  }
}

// 预览文档文件
const previewDocument = (filePath) => {
  // #ifdef H5
  // H5环境检查是否在微信浏览器中
  const isWechatBrowser = /micromessenger/i.test(navigator.userAgent)

  if (isWechatBrowser) {
    // 微信浏览器中直接打开文件，让微信处理预览
    window.open(filePath, '_blank')
  } else {
    // 普通H5浏览器跳转到预览页面
    uni.navigateTo({
      url: `/pages/transition/filePreview?filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(props.fileName)}`
    })
  }
  // #endif

  // #ifdef MP-WEIXIN
  // 微信小程序使用 wx.openDocument 直接预览
  uni.showLoading({
    title: '准备预览...',
    mask: true
  })

  uni.downloadFile({
    url: filePath,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          success: () => {
            uni.hideLoading()
            console.log('文档预览成功')
            // 预览成功后提示用户可以保存
            setTimeout(() => {
              uni.showToast({
                title: '可点击右上角保存到手机',
                icon: 'none',
                duration: 3000
              })
            }, 1000)
          },
          fail: (err) => {
            uni.hideLoading()
            console.error('文档预览失败:', err)
            toast.show('文档预览失败')
          }
        })
      } else {
        uni.hideLoading()
        toast.show('文件下载失败')
      }
    },
    fail: (err) => {
      uni.hideLoading()
      console.error('文件下载失败:', err)
      toast.show('文件下载失败')
    }
  })
  // #endif

  // #ifdef MP-ALIPAY
  // 支付宝小程序使用类似的方式
  uni.showLoading({
    title: '准备预览...',
    mask: true
  })

  uni.downloadFile({
    url: filePath,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          success: () => {
            uni.hideLoading()
            console.log('文档预览成功')
          },
          fail: (err) => {
            uni.hideLoading()
            console.error('文档预览失败:', err)
            toast.show('文档预览失败，请尝试下载')
            downloadFile()
          }
        })
      } else {
        uni.hideLoading()
        toast.show('文件下载失败')
      }
    },
    fail: (err) => {
      uni.hideLoading()
      console.error('文件下载失败:', err)
      toast.show('文件下载失败')
    }
  })
  // #endif

  // #ifdef APP-PLUS
  // APP环境也使用 openDocument
  uni.showLoading({
    title: '准备预览...',
    mask: true
  })

  uni.downloadFile({
    url: filePath,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          success: () => {
            uni.hideLoading()
            console.log('文档预览成功')
          },
          fail: (err) => {
            uni.hideLoading()
            console.error('文档预览失败:', err)
            toast.show('文档预览失败，请尝试下载')
            downloadFile()
          }
        })
      } else {
        uni.hideLoading()
        toast.show('文件下载失败')
      }
    },
    fail: (err) => {
      uni.hideLoading()
      console.error('文件下载失败:', err)
      toast.show('文件下载失败')
    }
  })
  // #endif
}

// 获取文件类型名称
const getFileTypeName = (type) => {
  const typeNames = {
    'image': '图片',
    'video': '视频',
    'document': '文档',
  }
  return typeNames[type] || '此类型'
}

// 下载文件
const downloadFile = () => {
  if (!fullFileUrl.value) {
    toast.show('文件路径为空')
    return
  }

  // #ifdef MP-WEIXIN
  // 微信小程序环境：直接下载文件到本地
  uni.showLoading({
    title: '文件下载中...',
    mask: true
  })

  uni.downloadFile({
    url: fullFileUrl.value,
    success: (res) => {
      if (res.statusCode === 200) {
        // 保存文件到本地
        uni.saveFile({
          tempFilePath: res.tempFilePath,
          success: (saveRes) => {
            uni.hideLoading()
            toast.show('文件已保存到本地')
            console.log('文件保存成功:', saveRes.savedFilePath)
          },
          fail: (saveErr) => {
            uni.hideLoading()
            console.error('文件保存失败:', saveErr)
            toast.show('文件保存失败')
          }
        })
      } else {
        uni.hideLoading()
        toast.show('文件下载失败')
      }
    },
    fail: (err) => {
      uni.hideLoading()
      console.error('文件下载失败:', err)
      toast.show('文件下载失败')
    }
  })
  // #endif

  // #ifndef MP-WEIXIN
  // 其他环境使用通用下载方法
  downloadAndOpenFile(fullFileUrl.value, {
    fileName: props.fileName
  })
  // #endif
}

// 删除文件
const deleteFile = () => {
  createModal({
    title: '确认删除',
    content: `确定要删除文件"${props.fileName}"吗？删除后将无法恢复`,
    confirmText: '确定',
    cancelText: '取消'
  }).then(res => {
    if (res.confirm) {
      // 触发删除事件，传递文件信息给父组件
      emit('delete', {
        filePath: props.filePath,
        fileName: props.fileName
      })
    }
  }).catch(error => {
    console.error('删除确认框异常:', error)
  })
}

// 表单保存校验方法
const handleSave = async (data = {}, options = {}) => {
  try {
    // 默认配置
    const defaultOptions = {
      showConfirm: true,
      validateRequired: true,
      showLoading: true,
      loadingText: '保存中...',
      successText: '保存成功',
      ...options
    }

    // 必填项校验
    if (defaultOptions.validateRequired) {
      const requiredFields = ['fileName', 'filePath']
      const missingFields = []

      requiredFields.forEach(field => {
        const value = data[field] || props[field]
        if (!value || (typeof value === 'string' && value.trim() === '')) {
          missingFields.push(field === 'fileName' ? '文件名称' : '文件路径')
        }
      })

      if (missingFields.length > 0) {
        toast.show(`请填写：${missingFields.join('、')}`)
        return {success: false, message: '必填项校验失败'}
      }
    }

    // 显示确认框
    if (defaultOptions.showConfirm) {
      const confirmResult = await createModal({
        title: '确认保存',
        content: '确定要保存当前文件信息吗？',
        confirmText: '确定',
        cancelText: '取消'
      })

      if (!confirmResult.confirm) {
        return {success: false, message: '用户取消操作'}
      }
    }

    // 显示加载状态
    if (defaultOptions.showLoading) {
      uni.showLoading({
        title: defaultOptions.loadingText,
        mask: true
      })
    }

    // 准备保存数据
    const saveData = {
      fileName: data.fileName || props.fileName,
      filePath: data.filePath || props.filePath,
      urlPrefix: data.urlPrefix || props.urlPrefix,
      ...data
    }

    // 触发保存事件，让父组件处理具体的保存逻辑
    emit('save', {
      data: saveData,
      options: defaultOptions
    })

    // 隐藏加载状态
    if (defaultOptions.showLoading) {
      uni.hideLoading()
    }

    // 显示成功提示
    if (defaultOptions.successText) {
      toast.show(defaultOptions.successText)
    }

    return {success: true, data: saveData}
  } catch (error) {
    // 隐藏加载状态
    uni.hideLoading()

    console.error('保存校验异常:', error)
    toast.show('保存失败，请重试')

    return {success: false, message: error.message || '保存异常'}
  }
}

// 导出方法供父组件调用
defineExpose({
  viewFile,
  deleteFile,
  handleSave
})
</script>

<style lang="scss" scoped>
.document-preview {
  width: 100%;
}

.file-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #f9fafc;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  border: 1px solid #eaecf0;

  &:active {
    background-color: #f0f2f5;
  }
}

.file-card-info {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.file-card-icon {
  font-size: 40rpx;
  margin-right: 12rpx;
}

.file-card-name {
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.file-type-tip {
  font-size: 24rpx;
  color: #999;
  margin-left: 12rpx;
  white-space: nowrap;
}

.file-card-btn {
  font-size: 28rpx;
  color: var(--nuodun-primary-color, #0078ff);
  padding: 8rpx 20rpx;
  background-color: rgba(var(--nuodun-primary-color-rgb, 0, 120, 255), 0.08);
  border-radius: 8rpx;
  white-space: nowrap;
}

.download-btn {
  margin-left: 10rpx;
}

.delete-btn {
  margin-left: 10rpx;
  color: #ff4d4f !important;
  background-color: rgba(255, 77, 79, 0.08) !important;

  &:active {
    background-color: rgba(255, 77, 79, 0.15) !important;
  }
}
</style>